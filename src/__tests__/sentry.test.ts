import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as vscode from 'vscode';

// Mock Sentry
const mockSentryInit = vi.fn();
const mockCaptureException = vi.fn();
const mockCaptureMessage = vi.fn();
const mockAddBreadcrumb = vi.fn();
const mockSetUser = vi.fn();
const mockSetTag = vi.fn();
const mockSetExtra = vi.fn();
const mockWithScope = vi.fn((callback) => callback({
  setTag: vi.fn(),
  setExtra: vi.fn(),
  setUser: vi.fn(),
  setLevel: vi.fn(),
}));

vi.mock('@sentry/node', () => ({
  init: mockSentryInit,
  captureException: mockCaptureException,
  captureMessage: mockCaptureMessage,
  addBreadcrumb: mockAddBreadcrumb,
  setUser: mockSetUser,
  setTag: mockSetTag,
  setExtra: mockSetExtra,
  withScope: mockWithScope,
}));

// Mock VS Code
vi.mock('vscode', () => ({
  version: '1.80.0',
}));

// Mock process.env for Sentry DSN
const originalEnv = process.env;
beforeEach(() => {
  process.env = { ...originalEnv, CODEBUDDY_SENTRY_DSN: 'https://<EMAIL>/123456' };
});

afterEach(() => {
  process.env = originalEnv;
});

// Mock extension functions
vi.mock('@/extension', () => ({
  appVersion: vi.fn(() => '1.0.0'),
  appChannel: vi.fn(() => 'stable'), // Use stable channel to enable Sentry
  currentUser: vi.fn(async () => 'testuser'),
}));

// Mock judge_env
vi.mock('@/judge_env', () => ({
  appEnv: vi.fn(() => 'test'),
}));

describe('Sentry Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset the initialization state
    vi.resetModules();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize Sentry with correct configuration', async () => {
    const { initSentry } = await import('@/sentry');
    const mockContext = {} as vscode.ExtensionContext;

    await initSentry(mockContext);

    expect(mockSentryInit).toHaveBeenCalledWith(
      expect.objectContaining({
        dsn: 'https://<EMAIL>/123456',
        environment: 'production',
        release: 'codebuddy@1.0.0',
        sampleRate: 0.1, // stable channel uses 10% sampling
        debug: false,
      })
    );
  });

  it('should not initialize Sentry in dev channel without DSN', async () => {
    // Mock dev channel without DSN
    vi.doMock('@/extension', () => ({
      appVersion: vi.fn(() => '1.0.0'),
      appChannel: vi.fn(() => 'dev'),
      currentUser: vi.fn(async () => 'testuser'),
    }));

    // Remove DSN from environment
    delete process.env.CODEBUDDY_SENTRY_DSN;

    const { initSentry } = await import('@/sentry');
    const mockContext = {} as vscode.ExtensionContext;

    await initSentry(mockContext);

    expect(mockSentryInit).not.toHaveBeenCalled();
  });

  it('should not initialize Sentry when DSN is missing in production', async () => {
    // Remove DSN from environment
    delete process.env.CODEBUDDY_SENTRY_DSN;

    const { initSentry } = await import('@/sentry');
    const mockContext = {} as vscode.ExtensionContext;

    await initSentry(mockContext);

    expect(mockSentryInit).not.toHaveBeenCalled();
  });

  it('should capture errors using simple interface', async () => {
    // First initialize Sentry
    const { initSentry } = await import('@/sentry');
    const mockContext = {} as vscode.ExtensionContext;
    await initSentry(mockContext);

    const { captureError } = await import('@/sentry/simple');
    const testError = new Error('Test error');

    captureError(testError, {
      tags: { component: 'test' },
      extra: { testData: 'value' },
      level: 'error',
    });

    expect(mockWithScope).toHaveBeenCalled();
  });

  it('should capture string messages', async () => {
    // First initialize Sentry
    const { initSentry } = await import('@/sentry');
    const mockContext = {} as vscode.ExtensionContext;
    await initSentry(mockContext);

    const { captureError } = await import('@/sentry/simple');

    captureError('Test message');

    expect(mockWithScope).toHaveBeenCalled();
  });

  it('should add breadcrumbs', async () => {
    // First initialize Sentry
    const { initSentry } = await import('@/sentry');
    const mockContext = {} as vscode.ExtensionContext;
    await initSentry(mockContext);

    const { addBreadcrumb } = await import('@/sentry/simple');

    addBreadcrumb('Test breadcrumb', 'test', 'info', { data: 'test' });

    expect(mockAddBreadcrumb).toHaveBeenCalledWith({
      message: 'Test breadcrumb',
      category: 'test',
      level: 'info',
      data: { data: 'test' },
    });
  });

  it('should handle errors gracefully when Sentry is not initialized', async () => {
    const { captureError } = await import('@/sentry/simple');
    const testError = new Error('Test error');

    // Should not throw when Sentry is not initialized
    expect(() => captureError(testError)).not.toThrow();
  });

  it('should filter sensitive URLs', async () => {
    const { initSentry } = await import('@/sentry');
    const mockContext = {} as vscode.ExtensionContext;

    await initSentry(mockContext);

    const initCall = mockSentryInit.mock.calls[0][0];
    const beforeSend = initCall.beforeSend;

    // Test URL sanitization
    const event = {
      request: {
        url: 'https://example.com/api?token=secret&key=value&password=hidden',
      },
    };

    const sanitizedEvent = beforeSend(event);
    expect(sanitizedEvent?.request?.url).not.toContain('token=secret');
    expect(sanitizedEvent?.request?.url).not.toContain('password=hidden');
  });

  it('should filter network errors', async () => {
    const { initSentry } = await import('@/sentry');
    const mockContext = {} as vscode.ExtensionContext;

    await initSentry(mockContext);

    const initCall = mockSentryInit.mock.calls[0][0];
    const beforeSend = initCall.beforeSend;

    // Test network error filtering
    const networkErrorEvent = {
      exception: {
        values: [{ value: 'Network Error: ECONNREFUSED' }],
      },
    };

    const result = beforeSend(networkErrorEvent);
    expect(result).toBeNull();
  });
});
