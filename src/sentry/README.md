# Sentry 错误监控集成

这是一个**对用户完全无感**的 Sentry 错误监控集成，为 CodeBuddy VS Code 扩展提供错误追踪和性能监控功能。

## 功能特性

- ✅ 自动错误捕获和上报
- ✅ 全局未捕获异常处理
- ✅ Promise 拒绝处理
- ✅ 敏感信息过滤
- ✅ 环境自动检测
- ✅ 用户上下文设置
- ✅ 性能监控支持
- ✅ 简化的错误上报接口
- ✅ **对用户完全透明，无需配置**

## 自动配置

Sentry 集成完全内置，根据扩展版本自动启用：

### 自动启用条件

- **生产环境** (`stable` channel): 自动启用，10% 采样率
- **测试环境** (`beta` channel): 自动启用，50% 采样率
- **开发环境** (`dev` channel): 仅在设置环境变量时启用，100% 采样率

### 环境变量配置（仅开发用）

```bash
export CODEBUDDY_SENTRY_DSN="https://<EMAIL>/project-id"
```

## 使用方法

### 基本错误捕获

```typescript
import { captureError } from '@/sentry/simple';

try {
  // 可能出错的代码
  riskyOperation();
} catch (error) {
  captureError(error, {
    tags: { component: 'completion' },
    extra: { userId: 'user123' },
    level: 'error'
  });
}
```

### 添加面包屑

```typescript
import { addBreadcrumb } from '@/sentry/simple';

addBreadcrumb('User clicked completion', 'user-action', 'info', {
  buttonId: 'accept-completion'
});
```

### 性能监控

```typescript
import { withPerformance } from '@/sentry/simple';

const monitoredFunction = withPerformance('api-call', async () => {
  return await apiCall();
});
```

### 设置用户上下文

```typescript
import { setUser, setTag, setExtra } from '@/sentry/simple';

setUser({ username: 'john_doe', email: '<EMAIL>' });
setTag('feature', 'completion');
setExtra('debugInfo', { version: '1.0.0' });
```

## 自动功能

### 全局错误处理

系统会自动捕获：

- 未捕获的异常 (`uncaughtException`)
- 未处理的 Promise 拒绝 (`unhandledRejection`)

### 敏感信息过滤

自动过滤以下敏感信息：

- URL 中的 token、key、password、secret 参数
- 网络连接错误 (ECONNREFUSED, Network Error, ENOTFOUND)

### 环境检测

自动检测运行环境：

- `redzone`: 红区环境
- `development`: 开发环境 (channel=dev)
- `production`: 生产环境

## 文件结构

```text
src/sentry/
├── index.ts      # 主要的 Sentry 初始化和配置
├── simple.ts     # 简化的错误上报接口
└── README.md     # 使用文档
```

## 测试

运行测试验证集成：

```bash
nvm use && npx vitest run src/__tests__/sentry.test.ts
```

## 注意事项

1. **隐私保护**: 系统会自动过滤敏感信息，但请确保不要在错误上下文中包含用户密码等敏感数据
2. **性能影响**: 生产环境使用低采样率（10%），对性能影响极小
3. **网络依赖**: Sentry 需要网络连接，离线环境下会静默失败
4. **初始化顺序**: Sentry 在扩展激活早期初始化，确保能捕获到大部分错误
5. **用户无感**: 用户无需任何配置，系统自动根据环境启用

## 故障排除

如果 Sentry 没有正常工作：

1. 检查环境变量 `CODEBUDDY_SENTRY_DSN` 是否设置（仅开发环境）
2. 验证 DSN 是否有效
3. 检查网络连接
4. 查看控制台日志中的 Sentry 初始化信息
