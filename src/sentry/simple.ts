import { Sentry, isSentryInitialized } from './index';

/**
 * 简化的错误上报接口
 */
export interface ErrorContext {
    /** 错误标签 */
    tags?: Record<string, string>;
    /** 额外的上下文信息 */
    extra?: Record<string, any>;
    /** 用户信息 */
    user?: {
        id?: string;
        username?: string;
        email?: string;
    };
    /** 错误级别 */
    level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug';
}

/**
 * 捕获异常并上报到 Sentry
 * @param error 错误对象
 * @param context 错误上下文
 */
export function captureError(error: Error | string, context?: ErrorContext): void {
    if (!isSentryInitialized()) {
        console.warn('[Sentry] Sentry not initialized, error not captured:', error);
        return;
    }

    try {
        // 设置上下文
        if (context) {
            Sentry.withScope((scope) => {
                if (context.tags) {
                    Object.entries(context.tags).forEach(([key, value]) => {
                        scope.setTag(key, value);
                    });
                }

                if (context.extra) {
                    Object.entries(context.extra).forEach(([key, value]) => {
                        scope.setExtra(key, value);
                    });
                }

                if (context.user) {
                    scope.setUser(context.user);
                }

                if (context.level) {
                    scope.setLevel(context.level);
                }

                // 捕获错误
                if (typeof error === 'string') {
                    Sentry.captureMessage(error);
                } else {
                    Sentry.captureException(error);
                }
            });
        } else {
            // 直接捕获错误
            if (typeof error === 'string') {
                Sentry.captureMessage(error);
            } else {
                Sentry.captureException(error);
            }
        }
    } catch (sentryError) {
        console.error('[Sentry] Failed to capture error:', sentryError);
    }
}

/**
 * 添加面包屑（用于追踪用户操作路径）
 * @param message 消息
 * @param category 分类
 * @param level 级别
 * @param data 额外数据
 */
export function addBreadcrumb(
    message: string,
    category?: string,
    level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug',
    data?: Record<string, any>
): void {
    if (!isSentryInitialized()) {
        return;
    }

    try {
        Sentry.addBreadcrumb({
            message,
            category: category || 'default',
            level: level || 'info',
            data,
            timestamp: Date.now() / 1000,
        });
    } catch (error) {
        console.error('[Sentry] Failed to add breadcrumb:', error);
    }
}

/**
 * 设置用户上下文
 * @param user 用户信息
 */
export function setUser(user: {
    id?: string;
    username?: string;
    email?: string;
    [key: string]: any;
}): void {
    if (!isSentryInitialized()) {
        return;
    }

    try {
        Sentry.setUser(user);
    } catch (error) {
        console.error('[Sentry] Failed to set user:', error);
    }
}

/**
 * 设置标签
 * @param key 标签键
 * @param value 标签值
 */
export function setTag(key: string, value: string): void {
    if (!isSentryInitialized()) {
        return;
    }

    try {
        Sentry.setTag(key, value);
    } catch (error) {
        console.error('[Sentry] Failed to set tag:', error);
    }
}

/**
 * 设置额外信息
 * @param key 键
 * @param value 值
 */
export function setExtra(key: string, value: any): void {
    if (!isSentryInitialized()) {
        return;
    }

    try {
        Sentry.setExtra(key, value);
    } catch (error) {
        console.error('[Sentry] Failed to set extra:', error);
    }
}

/**
 * 包装异步函数，自动捕获异常
 * @param fn 异步函数
 * @param context 错误上下文
 */
export function wrapAsync<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    context?: ErrorContext
): T {
    return (async (...args: any[]) => {
        try {
            return await fn(...args);
        } catch (error) {
            captureError(error as Error, context);
            throw error; // 重新抛出错误，保持原有的错误处理逻辑
        }
    }) as T;
}

/**
 * 包装同步函数，自动捕获异常
 * @param fn 同步函数
 * @param context 错误上下文
 */
export function wrapSync<T extends (...args: any[]) => any>(
    fn: T,
    context?: ErrorContext
): T {
    return ((...args: any[]) => {
        try {
            return fn(...args);
        } catch (error) {
            captureError(error as Error, context);
            throw error; // 重新抛出错误，保持原有的错误处理逻辑
        }
    }) as T;
}

/**
 * 性能监控装饰器
 * @param name 操作名称
 */
export function withPerformance<T extends (...args: any[]) => any>(
    name: string,
    fn: T
): T {
    return ((...args: any[]) => {
        if (!isSentryInitialized()) {
            return fn(...args);
        }

        const transaction = Sentry.startTransaction({
            name,
            op: 'function',
        });

        try {
            const result = fn(...args);

            // 如果是 Promise，等待完成
            if (result && typeof result.then === 'function') {
                return result
                    .then((value: any) => {
                        transaction.setStatus('ok');
                        transaction.finish();
                        return value;
                    })
                    .catch((error: any) => {
                        transaction.setStatus('internal_error');
                        transaction.finish();
                        throw error;
                    });
            } else {
                transaction.setStatus('ok');
                transaction.finish();
                return result;
            }
        } catch (error) {
            transaction.setStatus('internal_error');
            transaction.finish();
            throw error;
        }
    }) as T;
}