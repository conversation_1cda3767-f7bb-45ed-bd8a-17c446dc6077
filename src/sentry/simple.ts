import { Sentry, isSentryInitialized } from './index';

/**
 * 简化的错误上报接口
 */
export interface ErrorContext {
    /** 错误标签 */
    tags?: Record<string, string>;
    /** 额外的上下文信息 */
    extra?: Record<string, any>;
    /** 用户信息 */
    user?: {
        id?: string;
        username?: string;
        email?: string;
    };
    /** 错误级别 */
    level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug';
}

/**
 * 捕获异常并上报到 Sentry
 * @param error 错误对象
 * @param context 错误上下文
 */
export function captureError(error: Error | string, context?: ErrorContext): void {
    if (!isSentryInitialized()) {
        console.warn('[Sentry] Sentry not initialized, error not captured:', error);
        return;
    }

    try {
        // 设置上下文
        if (context) {
            Sentry.withScope((scope) => {
                if (context.tags) {
                    Object.entries(context.tags).forEach(([key, value]) => {
                        scope.setTag(key, value);
                    });
                }

                if (context.extra) {
                    Object.entries(context.extra).forEach(([key, value]) => {
                        scope.setExtra(key, value);
                    });
                }

                if (context.user) {
                    scope.setUser(context.user);
                }

                if (context.level) {
                    scope.setLevel(context.level);
                }

                // 捕获错误
                if (typeof error === 'string') {
                    Sentry.captureMessage(error);
                } else {
                    Sentry.captureException(error);
                }
            });
        } else {
            // 直接捕获错误
            if (typeof error === 'string') {
                Sentry.captureMessage(error);
            } else {
                Sentry.captureException(error);
            }
        }
    } catch (sentryError) {
        console.error('[Sentry] Failed to capture error:', sentryError);
    }
}

/**
 * 添加面包屑信息
 * @param message 消息
 * @param category 分类
 * @param level 级别
 * @param data 额外数据
 */
export function addBreadcrumb(
    message: string,
    category?: string,
    level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug',
    data?: Record<string, any>
): void {
    if (!isSentryInitialized()) {
        return;
    }

    try {
        Sentry.addBreadcrumb({
            message,
            category: category || 'default',
            level: level || 'info',
            data,
        });
    } catch (sentryError) {
        console.error('[Sentry] Failed to add breadcrumb:', sentryError);
    }
}

/**
 * 设置用户上下文
 * @param user 用户信息
 */
export function setUser(user: { id?: string; username?: string; email?: string }): void {
    if (!isSentryInitialized()) {
        return;
    }

    try {
        Sentry.setUser(user);
    } catch (sentryError) {
        console.error('[Sentry] Failed to set user:', sentryError);
    }
}

/**
 * 设置标签
 * @param key 标签键
 * @param value 标签值
 */
export function setTag(key: string, value: string): void {
    if (!isSentryInitialized()) {
        return;
    }

    try {
        Sentry.setTag(key, value);
    } catch (sentryError) {
        console.error('[Sentry] Failed to set tag:', sentryError);
    }
}

/**
 * 设置额外上下文
 * @param key 上下文键
 * @param value 上下文值
 */
export function setExtra(key: string, value: any): void {
    if (!isSentryInitialized()) {
        return;
    }

    try {
        Sentry.setExtra(key, value);
    } catch (sentryError) {
        console.error('[Sentry] Failed to set extra:', sentryError);
    }
}

/**
 * 性能监控 - 开始事务
 * @param name 事务名称
 * @param op 操作类型
 * @returns 事务对象
 */
export function startTransaction(name: string, op?: string) {
    if (!isSentryInitialized()) {
        return null;
    }

    try {
        return Sentry.startTransaction({ name, op: op || 'default' });
    } catch (sentryError) {
        console.error('[Sentry] Failed to start transaction:', sentryError);
        return null;
    }
}

/**
 * 简化的性能监控装饰器
 * @param name 操作名称
 */
export function withPerformance<T extends (...args: any[]) => any>(
    name: string,
    fn: T
): T {
    return ((...args: any[]) => {
        const transaction = startTransaction(name);
        try {
            const result = fn(...args);
            if (result instanceof Promise) {
                return result.finally(() => {
                    transaction?.finish();
                });
            } else {
                transaction?.finish();
                return result;
            }
        } catch (error) {
            transaction?.finish();
            throw error;
        }
    }) as T;
}
