/**
 * Sentry 集成使用示例
 * 
 * 这个文件展示了如何在 CodeBuddy 扩展中使用 Sentry 进行错误监控
 */

import { captureError, addBreadcrumb, setUser, withPerformance } from '@/sentry/simple';

// 示例 1: 基本错误捕获
export async function exampleBasicErrorCapture() {
  try {
    // 模拟一个可能失败的操作
    await someRiskyOperation();
  } catch (error) {
    // 捕获错误并上报到 Sentry
    captureError(error as Error, {
      tags: {
        component: 'completion',
        operation: 'code-generation'
      },
      extra: {
        timestamp: new Date().toISOString(),
        userAction: 'accept-suggestion'
      },
      level: 'error'
    });
    
    // 继续处理错误或重新抛出
    throw error;
  }
}

// 示例 2: 在 API 调用中使用
export async function exampleApiCall(endpoint: string, data: any) {
  // 添加面包屑记录操作
  addBreadcrumb(`API call to ${endpoint}`, 'http', 'info', {
    endpoint,
    method: 'POST'
  });

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    captureError(error as Error, {
      tags: {
        component: 'api',
        endpoint: endpoint.replace(/https?:\/\/[^\/]+/, '') // 移除域名保护隐私
      },
      extra: {
        httpStatus: (error as any).status,
        requestData: data
      },
      level: 'error'
    });
    throw error;
  }
}

// 示例 3: 性能监控
export const monitoredCodeCompletion = withPerformance('code-completion', async (context: any) => {
  addBreadcrumb('Starting code completion', 'completion', 'info');
  
  try {
    // 模拟代码补全逻辑
    const suggestions = await generateCodeSuggestions(context);
    
    addBreadcrumb(`Generated ${suggestions.length} suggestions`, 'completion', 'info', {
      suggestionCount: suggestions.length
    });
    
    return suggestions;
  } catch (error) {
    captureError(error as Error, {
      tags: {
        component: 'completion',
        operation: 'generate-suggestions'
      },
      extra: {
        context: sanitizeContext(context)
      },
      level: 'error'
    });
    throw error;
  }
});

// 示例 4: 用户认证错误处理
export async function exampleAuthError(authResult: any) {
  if (!authResult.success) {
    // 设置用户上下文（如果有的话）
    if (authResult.userId) {
      setUser({
        id: authResult.userId,
        username: authResult.username
      });
    }

    captureError('Authentication failed', {
      tags: {
        component: 'auth',
        errorType: 'authentication'
      },
      extra: {
        errorCode: authResult.errorCode,
        errorMessage: authResult.errorMessage,
        // 注意：不要包含密码等敏感信息
      },
      level: 'warning'
    });
  }
}

// 示例 5: 在命令处理中使用
export async function exampleCommandHandler(command: string, args: any[]) {
  addBreadcrumb(`Executing command: ${command}`, 'command', 'info', {
    command,
    argCount: args.length
  });

  try {
    switch (command) {
      case 'codebuddy.explain':
        return await handleExplainCommand(args);
      case 'codebuddy.fix':
        return await handleFixCommand(args);
      default:
        throw new Error(`Unknown command: ${command}`);
    }
  } catch (error) {
    captureError(error as Error, {
      tags: {
        component: 'command-handler',
        command: command
      },
      extra: {
        args: sanitizeArgs(args)
      },
      level: 'error'
    });
    throw error;
  }
}

// 示例 6: 在文件操作中使用
export async function exampleFileOperation(filePath: string) {
  try {
    addBreadcrumb(`Reading file: ${filePath}`, 'file-io', 'info');
    
    // 模拟文件操作
    const content = await readFile(filePath);
    return content;
  } catch (error) {
    captureError(error as Error, {
      tags: {
        component: 'file-io',
        operation: 'read'
      },
      extra: {
        filePath: sanitizeFilePath(filePath), // 移除用户路径信息
        errorCode: (error as any).code
      },
      level: 'error'
    });
    throw error;
  }
}

// 辅助函数：清理敏感信息
function sanitizeContext(context: any): any {
  // 移除可能包含敏感信息的字段
  const { password, token, secret, ...safeContext } = context;
  return safeContext;
}

function sanitizeArgs(args: any[]): any[] {
  return args.map(arg => {
    if (typeof arg === 'object' && arg !== null) {
      return sanitizeContext(arg);
    }
    return arg;
  });
}

function sanitizeFilePath(filePath: string): string {
  // 只保留文件名和扩展名，移除完整路径
  return filePath.split('/').pop() || filePath;
}

// 模拟函数（实际使用时替换为真实实现）
async function someRiskyOperation(): Promise<void> {
  throw new Error('Something went wrong');
}

async function generateCodeSuggestions(context: any): Promise<string[]> {
  return ['suggestion1', 'suggestion2'];
}

async function handleExplainCommand(args: any[]): Promise<string> {
  return 'Explanation result';
}

async function handleFixCommand(args: any[]): Promise<string> {
  return 'Fix result';
}

async function readFile(filePath: string): Promise<string> {
  return 'file content';
}
