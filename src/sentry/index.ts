import * as Sentry from '@sentry/node';
import { getGlobalConf } from '@/config';
import { appVersion, appChannel, currentUser } from '@/extension';
import * as vscode from 'vscode';
import * as os from 'os';

interface SentryConfig {
    dsn?: string;
    environment?: string;
    enabled?: boolean;
    sampleRate?: number;
    debug?: boolean;
}

let isInitialized = false;

/**
 * 初始化 Sentry
 * @param context VSCode 扩展上下文
 */
export async function initSentry(context: vscode.ExtensionContext): Promise<void> {
    if (isInitialized) {
        return;
    }

    try {
        const config = getSentryConfig();

        if (!config.enabled || !config.dsn) {
            console.log('[Sentry] Sentry is disabled or DSN not configured');
            return;
        }

        // 获取用户信息
        const username = await currentUser();

        Sentry.init({
            dsn: config.dsn,
            environment: config.environment || 'production',
            release: `codebuddy@${appVersion()}`,
            sampleRate: config.sampleRate || 1.0,
            debug: config.debug || false,

            // 设置用户上下文
            initialScope: {
                user: {
                    username: username,
                },
                tags: {
                    version: appVersion(),
                    channel: appChannel(),
                    platform: os.platform(),
                    arch: os.arch(),
                    vscode_version: vscode.version,
                },
                contexts: {
                    os: {
                        name: os.platform(),
                        version: os.release(),
                    },
                    runtime: {
                        name: 'node',
                        version: process.version,
                    },
                },
            },

            // 过滤敏感信息
            beforeSend(event) {
                // 移除可能包含敏感信息的数据
                if (event.request?.url) {
                    event.request.url = sanitizeUrl(event.request.url);
                }

                // 过滤掉一些不重要的错误
                if (event.exception?.values) {
                    for (const exception of event.exception.values) {
                        if (exception.value?.includes('ECONNREFUSED') ||
                            exception.value?.includes('Network Error')) {
                            return null; // 不上报网络错误
                        }
                    }
                }

                return event;
            },

            // 集成配置
            integrations: [
                // 默认集成已经包含了大部分需要的功能
            ],
        });

        // 设置全局错误处理
        setupGlobalErrorHandlers();

        isInitialized = true;
        console.log('[Sentry] Sentry initialized successfully');

        // 发送初始化事件
        Sentry.addBreadcrumb({
            message: 'Sentry initialized',
            level: 'info',
            category: 'sentry',
        });

    } catch (error) {
        console.error('[Sentry] Failed to initialize Sentry:', error);
    }
}

/**
 * 获取 Sentry 配置
 */
function getSentryConfig(): SentryConfig {
    const config = getGlobalConf();

    return {
        dsn: config.get('sentry.dsn', ''),
        environment: config.get('sentry.environment', 'production'),
        enabled: config.get('sentry.enabled', false),
        sampleRate: config.get('sentry.sampleRate', 1.0),
        debug: config.get('sentry.debug', false),
    };
}

/**
 * 设置全局错误处理
 */
function setupGlobalErrorHandlers(): void {
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
        console.error('[Sentry] Uncaught Exception:', error);
        Sentry.captureException(error);
    });

    // 处理未处理的 Promise 拒绝
    process.on('unhandledRejection', (reason, promise) => {
        console.error('[Sentry] Unhandled Rejection at:', promise, 'reason:', reason);
        Sentry.captureException(reason);
    });
}

/**
 * 清理敏感的 URL 信息
 */
function sanitizeUrl(url: string): string {
    try {
        const urlObj = new URL(url);
        // 移除查询参数中可能的敏感信息
        urlObj.search = '';
        // 移除用户信息
        urlObj.username = '';
        urlObj.password = '';
        return urlObj.toString();
    } catch {
        return '[SANITIZED_URL]';
    }
}

/**
 * 检查 Sentry 是否已初始化
 */
export function isSentryInitialized(): boolean {
    return isInitialized;
}

/**
 * 获取 Sentry 实例（用于高级用法）
 */
export { Sentry };