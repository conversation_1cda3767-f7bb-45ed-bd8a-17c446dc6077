import * as Sentry from '@sentry/node';
import { appVersion, appChannel, currentUser } from '@/extension';
import { appEnv } from '@/judge_env';
import * as vscode from 'vscode';
import * as os from 'os';

interface SentryConfig {
    dsn: string;
    environment: string;
    enabled: boolean;
    sampleRate: number;
    debug: boolean;
}

let isInitialized = false;

/**
 * 初始化 Sentry
 * @param _context VSCode 扩展上下文
 */
export async function initSentry(_context: vscode.ExtensionContext): Promise<void> {
    if (isInitialized) {
        return;
    }

    try {
        const config = getSentryConfig();

        if (!config.enabled || !config.dsn) {
            console.log('[Sentry] Sentry is disabled or DSN not configured');
            return;
        }

        // 获取用户信息
        const username = await currentUser();

        Sentry.init({
            dsn: config.dsn,
            environment: config.environment || getEnvironment(),
            release: `codebuddy@${appVersion()}`,
            sampleRate: config.sampleRate || 1.0,
            debug: config.debug || false,

            // 设置用户上下文
            initialScope: {
                user: {
                    username: username,
                },
                tags: {
                    version: appVersion(),
                    channel: appChannel(),
                    platform: os.platform(),
                    arch: os.arch(),
                    vscodeVersion: vscode.version,
                    appEnv: appEnv(),
                },
                contexts: {
                    os: {
                        name: os.platform(),
                        version: os.release(),
                    },
                    runtime: {
                        name: 'node',
                        version: process.version,
                    },
                },
            },

            // 过滤敏感信息
            beforeSend(event) {
                // 移除可能包含敏感信息的数据
                if (event.request?.url) {
                    event.request.url = sanitizeUrl(event.request.url);
                }

                // 过滤掉一些不重要的错误
                if (event.exception?.values) {
                    for (const exception of event.exception.values) {
                        if (exception.value?.includes('ECONNREFUSED') ||
                            exception.value?.includes('Network Error') ||
                            exception.value?.includes('ENOTFOUND')) {
                            return null; // 不上报网络错误
                        }
                    }
                }

                return event;
            },

            // 集成配置
            integrations: [
                // 默认集成已经包含了大部分需要的功能
            ],
        });

        // 设置全局错误处理
        setupGlobalErrorHandlers();

        isInitialized = true;
        console.log('[Sentry] Sentry initialized successfully');

        // 发送初始化事件
        Sentry.addBreadcrumb({
            message: 'Sentry initialized',
            level: 'info',
            category: 'sentry',
        });

    } catch (error) {
        console.error('[Sentry] Failed to initialize Sentry:', error);
    }
}

/**
 * 获取 Sentry 配置 - 完全内置，对用户无感
 */
function getSentryConfig(): SentryConfig {
    const channel = appChannel();

    // 根据环境自动配置
    let dsn = '';
    let enabled = false;

    // 只在生产环境启用 Sentry
    if (channel === 'stable' || channel === 'beta') {
        enabled = true;
        // TODO: 替换为实际的 Sentry DSN
        dsn = process.env.CODEBUDDY_SENTRY_DSN || '';
    }

    // 开发环境可以通过环境变量启用
    if (channel === 'dev' && process.env.CODEBUDDY_SENTRY_DSN) {
        enabled = true;
        dsn = process.env.CODEBUDDY_SENTRY_DSN;
    }

    return {
        dsn,
        environment: getEnvironment(),
        enabled,
        sampleRate: getSampleRate(),
        debug: channel === 'dev',
    };
}

/**
 * 获取环境名称
 */
function getEnvironment(): string {
    const env = appEnv();
    const channel = appChannel();

    if (env.includes('redzone')) {
        return 'redzone';
    }

    if (channel === 'dev') {
        return 'development';
    }

    return 'production';
}

/**
 * 获取采样率
 */
function getSampleRate(): number {
    const channel = appChannel();

    // 开发环境使用 100% 采样率便于调试
    if (channel === 'dev') {
        return 1.0;
    }

    // 生产环境使用较低的采样率以减少性能影响
    if (channel === 'stable') {
        return 0.1; // 10%
    }

    // Beta 环境使用中等采样率
    if (channel === 'beta') {
        return 0.5; // 50%
    }

    return 1.0;
}

/**
 * 设置全局错误处理
 */
function setupGlobalErrorHandlers(): void {
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
        console.error('[Sentry] Uncaught Exception:', error);
        Sentry.captureException(error);
    });

    // 处理未处理的 Promise 拒绝
    process.on('unhandledRejection', (reason, promise) => {
        console.error('[Sentry] Unhandled Rejection at:', promise, 'reason:', reason);
        Sentry.captureException(reason);
    });
}

/**
 * 清理敏感信息的 URL
 */
function sanitizeUrl(url: string): string {
    try {
        const urlObj = new URL(url);
        // 移除查询参数中的敏感信息
        urlObj.searchParams.delete('token');
        urlObj.searchParams.delete('key');
        urlObj.searchParams.delete('password');
        urlObj.searchParams.delete('secret');
        return urlObj.toString();
    } catch {
        return '[INVALID_URL]';
    }
}

/**
 * 检查 Sentry 是否已初始化
 */
export function isSentryInitialized(): boolean {
    return isInitialized;
}

/**
 * 导出 Sentry 实例
 */
export { Sentry };
